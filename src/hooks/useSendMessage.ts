import {
  CbEvents,
  MessageStatus,
  MessageType,
  ConversationItem,
} from '@ht/openim-wasm-client-sdk';
import { MessageItem } from '@ht/openim-wasm-client-sdk/lib/types/entity';
import { SendMsgParams } from '@ht/openim-wasm-client-sdk/lib/types/params';
import { useCallback, useState } from 'react';
import { IMSDK } from '@/layouts/BasicLayout';
import { getDefaultThreadConversation } from '@/utils/utils';
import { PromptModal } from '@/components/Channel/components/MessageListForeword/RobotConfigModal';
import { isEqual, throttle } from 'lodash';
import { useConversationStore } from '@/store';
import { shallow } from 'zustand/shallow';
import { AILoadingText } from '@/utils/constants';
import { pushNewMessage, updateOneMessage } from './useHistoryMessageList';
import useThreadState from './useThreadState';

interface streamMessageProps {
  recvUser: any;
  curMsg: MessageItem;
  lastMsg?: MessageItem;
}

export type SendMessageParams = Partial<Omit<SendMsgParams, 'message'>> & {
  message: MessageItem;
  needPush?: boolean;
  inRightThread?: boolean; // 闭包问题，新创建的thread框里，初始化时conversation传的值为空，第一次sendMessage拿不到更新后的currentThreadConversation，需要在函数里面查询
};
export function useSendMessage(conversation: any) {
  const { updateLlmLoading, updateLlmQuestions } = useConversationStore(
    (state) => ({
      updateLlmLoading: state.updateLlmLoading,
      updateLlmQuestions: state.updateLlmQuestions,
    }),
    shallow
  );

  const { updateBotConfigModal, botConfig, currentBotConfig } =
    useConversationStore(
      (state) => ({
        updateBotConfigModal: state.updateBotConfigModal,
        botConfig: state.botConfig,
        currentBotConfig: state.currentBotConfig,
      }),
      shallow
    );
  const { createThread, joinThread } = useThreadState();
  const throttledUpdateOneMessage = throttle(updateOneMessage, 100);

  const sendMessage = useCallback(
    async ({
      recvID,
      groupID,
      message,
      needPush,
      inRightThread,
    }: SendMessageParams) => {
      if (inRightThread && groupID == null) {
        console.error(
          'thread中初始化sendMessage失败',
          { inRightThread },
          { groupID }
        );
        return;
      }
      const currentConversation = inRightThread
        ? getDefaultThreadConversation(groupID as string)
        : conversation;

      if (conversation === null) {
        console.error(
          '发送消息失败，原因是：调用sendMessage时，conversation为空'
        );
        return;
      }
      const sourceID = recvID || groupID;
      const inCurrentConversation =
        currentConversation?.userID === sourceID ||
        currentConversation?.groupID === sourceID ||
        !sourceID;

      needPush = needPush ?? inCurrentConversation;
      if (needPush) {
        pushNewMessage({
          ...message,
          groupID: groupID ?? currentConversation?.groupID ?? '',
        });
      }

      const options = {
        recvID: recvID ?? currentConversation?.userID ?? '',
        groupID: groupID ?? currentConversation?.groupID ?? '',
        message,
      };

      try {
        const { data: successMessage } = await IMSDK.sendMessage(options);

        updateOneMessage(successMessage);
      } catch (error) {
        console.error('发消息失败了', error);
        updateOneMessage({
          ...message,
          status: MessageStatus.Failed,
        });
      }
    },
    [conversation]
  );

  // 右侧消息列发送消息
  const sendThreadMessage = async (
    message: MessageItem,
    inRightThread: boolean | undefined
  ) => {
    // 在已创建的thread中打开，点击发送
    if (conversation?.groupID != null) {
      const joinThreadResult = await joinThread(conversation?.groupID);
      if (joinThreadResult) {
        await sendMessage({ message });
      }
    } // 首次打开，需先创建thread
    else {
      const threadID = await createThread();

      if (threadID != null) {
        await sendMessage({
          message,
          groupID: threadID,
          inRightThread,
        });
      } else {
        console.error('创建thread失败，原因是threadID');
      }
    }
  };

  // 检查机器人配置是否有效，返回配置参数或undefined（表示配置无效）
  const checkBotConfig = useCallback(() => {
    if (botConfig && botConfig.length > 0) {
      const currentConfig = currentBotConfig?.config;
      const isRequired = botConfig.some((item) => item.required === 1);
      if (!currentConfig) {
        if (isRequired) {
          PromptModal({
            promptText: '请先填写对话变量，再开启对话',
            showFooterCancel: true,
            onOk: () => {
              updateBotConfigModal(true);
            },
            onOkText: '配置变量',
          });
          return undefined;
        }
      } else if (!isEqual(botConfig, currentConfig)) {
        PromptModal({
          promptText: '请先修改对话变量，再开启对话',
          showFooterCancel: true,
          onOk: () => {
            updateBotConfigModal(true, 'reset');
          },
          onOkText: '配置变量',
        });
        return undefined;
      }
      return currentBotConfig?.data || {};
    }
    return {};
  }, [botConfig, currentBotConfig, updateBotConfigModal]);

  const sendStreamMessage = async ({
    recvUser,
    curMsg,
    lastMsg,
  }: streamMessageProps) => {
    const parameters = checkBotConfig();
    if (parameters === undefined) {
      return;
    }
    updateLlmLoading(true);
    updateLlmQuestions([], '');

    const handleBotMessage = ({ data }: any) => {
      onReceiveMessage(data);
      console.log('handleBotMessage', data);

      if (data.event === 'message_end') {
        updateLlmLoading(false);
        IMSDK.off(CbEvents.OnSendMessageToBot, handleBotMessage);
      }
    };

    // 用于存储流式消息的中间状态（放在组件作用域，避免多次声明）
    const streamMessageMap: Record<string, any> = {};

    const onReceiveMessage = (data: any) => {
      // 解析事件类型和message_id
      const { event } = data;
      const receiveData = JSON.parse(data.data);
      const messageId = receiveData.clientMsgId;

      if (!messageId) {
        return;
      }
      // 初始化或获取当前流式消息对象
      if (!streamMessageMap[messageId]) {
        streamMessageMap[messageId] = {
          type: 'stream',
          content: {
            id: messageId,
            answer: '',
            receiveData: receiveData.created_at || Date.now(),
          },
        };
      }
      const msg = streamMessageMap[messageId];
      const { content } = msg;

      // 处理不同事件
      switch (event) {
        case 'message_start':
          content.start = receiveData.created_at;
          break;
        case 'message': {
          content.answer += receiveData.answer;
          break;
        }
        case 'message_end':
          content.end = receiveData.created_at;
          break;
        case 'message_cost':
          content.input_tokens = receiveData.input_tokens;
          content.output_tokens = receiveData.output_tokens;
          content.latency = receiveData.latency;
          return;
        case 'think_message':
          if (typeof receiveData.answer === 'string') {
            content.think = {
              answer: (content.think?.answer || '') + receiveData.answer,
            };
          }
          break;
        case 'suggestion':
          updateLlmQuestions(receiveData.questions, receiveData.clientMsgId);
          break;
        case 'message_failed':
          content.error = {
            code: receiveData.code,
            msg: receiveData.error,
          };
          content.end = receiveData.created_at;
          break;

        case 'knowledge_retrieve_end':
          content.knowledge_retrieve = {
            ...content.knowledge_retrieve,
            end: receiveData.created_at,
            latency: receiveData.latency,
            results: receiveData.results,
          };
          break;
        // case 'qa_retrieve_end':
        //   content.qa_retrieve = {
        //     ...content.qa_retrieve,
        //     end: data.created_at,
        //     latency: data.latency,
        //     results: data.results,
        //   };
        //   break;
        // case 'network_search':
        //   content.network_search = content.network_search || {
        //     start: data.created_at,
        //     results: [],
        //   };
        //   break;
        case 'network_search_end':
          content.network_search = {
            ...content.network_search,
            end: receiveData.created_at,
            latency: receiveData.latency,
            results: receiveData.results,
          };
          break;
        default:
          break;
      }

      if (content.answer || content.think?.answer) {
        updateOneMessage({
          clientMsgID: receiveData.clientMsgId,
          customElem: {
            data: JSON.stringify({
              type: 'stream',
              content,
            }),
          },
        } as MessageItem);
      }
    };

    IMSDK.on(CbEvents.OnSendMessageToBot, handleBotMessage);
    await IMSDK.sendMsgToBotV2({
      recvID: recvUser.userID,
      message: lastMsg || curMsg,
      // receiverNickname: recvUser.nickname,
      subConversationID: conversation?.subConversationId || '',
      // 如果有上一条消息，说明是重新生成，那么seq使用当前消息的seq，否则使用0
      seq: lastMsg ? curMsg.seq : 0,
      parameters: parameters ? parameters : {},
    });
  };

  return {
    sendMessage,
    sendThreadMessage,
    sendStreamMessage,
  };
}
